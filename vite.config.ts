import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import type { UserConfig } from 'vite';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const isMockMode = process?.env?.VITE_USE_MOCK_SERVER === 'true';
  
  const config: UserConfig = {
    plugins: [react()],
    publicDir: 'public', // Ensure public files are copied to dist
    server: {
      port: 5173,
      strictPort: true, // Fail if port is already in use
      proxy: {
        // Proxy para API do backend quando não estiver usando mock
        '/api': {
          target: process?.env?.VITE_API_BASE_URL || 'https://cognit-ai-s3q92.ondigitalocean.app',
          changeOrigin: true,
          secure: true, // Para HTTPS
          configure: (proxy, _options) => {
            proxy.on('error', (err, _req, _res) => {
              if (typeof console !== 'undefined') {
                console.log('proxy error', err);
              }
            });
            proxy.on('proxyReq', (proxyReq, req, _res) => {
              if (typeof console !== 'undefined') {
                console.log('Sending Request to the Target:', req.method, req.url);
              }
            });
            proxy.on('proxyRes', (proxyRes, req, _res) => {
              if (typeof console !== 'undefined') {
                console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
              }
            });
          },
        }
      }
    },
    optimizeDeps: {
      include: ['lucide-react'],
    },
    build: {
      // Remove console.logs em produção EXCETO quando em modo mock
      minify: 'terser',
      terserOptions: {
        compress: {
          // Preserva console.logs se estiver em modo mock
          drop_console: mode === 'production' && !isMockMode,
          drop_debugger: mode === 'production' && !isMockMode,
        },
      },
    },
  };
  
  return config;
});
