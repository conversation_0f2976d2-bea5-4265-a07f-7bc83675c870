{"name": "cognit-ai-platform", "private": true, "version": "2.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build && cp dist/index.html dist/404.html", "lint": "eslint src --ext .ts,.tsx --max-warnings 0", "typecheck": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest", "preview": "vite preview"}, "dependencies": {"@azure/msal-browser": "^4.15.0", "@azure/msal-react": "^3.0.15", "@headlessui/react": "^2.2.4", "@reduxjs/toolkit": "^2.8.2", "@tanstack/react-query": "^5.83.0", "axios": "^1.10.0", "framer-motion": "^12.23.6", "katex": "^0.16.22", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-katex": "^3.1.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.7.0", "redux-persist": "^6.0.0", "remark-gfm": "^4.0.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/katex": "^0.16.7", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-katex": "^3.0.4", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^3.2.4", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jsdom": "^26.1.0", "miragejs": "^0.1.48", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "terser": "^5.43.1", "typescript": "~5.5.4", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vitest": "^3.2.4"}, "overrides": {"esbuild": "^0.25.0"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}}