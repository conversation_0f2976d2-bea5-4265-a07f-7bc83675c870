import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
  sendMessage,
  uploadFile,
  fetchConversations,
  fetchConversation,
  deleteConversation,
  updateConversationTitle
} from './chatConversationsActions';
import type {
  ChatConversationsState,
  Conversation,
  Message,
  ConversationFilters
} from '../../types/chat';

const initialState: ChatConversationsState = {
  // Lista de conversas
  conversations: [],
  
  // Conversa ativa
  currentConversation: null,
  
  // Mensagens da conversa ativa
  messages: [],
  
  // Estado de UI
  isLoading: false,
  isTyping: false,
  streamingMessage: null,
  error: null,
  
  // Seleções do usuário
  selectedProvider: 'openai',
  selectedModel: 'gpt-4o',
  
  // Filtros e busca
  searchQuery: '',
  filters: {},
  favoriteConversations: [],
  
  // Paginação
  hasMore: true,
  
  // Estado temporário
  pendingSessionId: null,
};

const chatConversationsSlice = createSlice({
  name: 'chatConversations',
  initialState,
  reducers: {
    // Ações de conversa
    setCurrentConversation: (state, action: PayloadAction<Conversation | null>) => {
      const newConversation = action.payload;
      
      // Se é uma nova conversa sendo criada, não limpar typing/streaming
      const isCreatingNewSession = newConversation && 
          !state.currentConversation?.sessionId && 
          newConversation.sessionId;
      
      // Se mudou de conversa (e não é criação de sessão), limpar streaming
      if (state.currentConversation?.sessionId !== newConversation?.sessionId && !isCreatingNewSession) {
        state.streamingMessage = null;
        state.isTyping = false;
      }
          
      if (isCreatingNewSession && state.messages.length > 0) {
        // SEMPRE preservar mensagens existentes quando criando nova sessão
        
        state.currentConversation = {
          ...newConversation,
          messages: [...state.messages],
          messageCount: state.messages.length
        };
        // Não alterar state.messages pois já estão corretas
      } else {
        // Caso normal - definir conversa e suas mensagens
        state.currentConversation = newConversation;
        state.messages = newConversation?.messages || [];
      }
      
      // Se é uma conversa existente, garantir que está na lista
      if (newConversation && !state.conversations.find(c => c.sessionId === newConversation.sessionId)) {
        state.conversations.unshift(state.currentConversation || newConversation);
      }
    },
    
    startNewConversation: (state) => {
      state.currentConversation = null;
      state.messages = [];
      state.streamingMessage = null;
      state.isTyping = false;
      state.pendingSessionId = null;
      state.error = null;
    },
    
    // Ações de mensagem
    addMessage: (state, action: PayloadAction<Message>) => {
      const message = action.payload;
      
      // Adicionar à lista de mensagens
      state.messages.push(message);
      
      // Atualizar conversa atual se existir
      if (state.currentConversation) {
        state.currentConversation.messages.push(message);
        state.currentConversation.messageCount = state.messages.length;
        state.currentConversation.lastMessageAt = message.timestamp;
        state.currentConversation.updatedAt = message.timestamp;
        
        // Atualizar na lista de conversas
        const conversationIndex = state.conversations.findIndex(
          c => c.sessionId === state.currentConversation!.sessionId
        );
        if (conversationIndex !== -1) {
          state.conversations[conversationIndex] = { ...state.currentConversation };
        }
      }
    },
    
    updateMessage: (state, action: PayloadAction<{ messageId: string; content: string }>) => {
      const { messageId, content } = action.payload;
      
      // Atualizar nas mensagens atuais
      const messageIndex = state.messages.findIndex(m => m.id === messageId);
      if (messageIndex !== -1) {
        state.messages[messageIndex].content = content;
      }
      
      // Atualizar na conversa atual
      if (state.currentConversation) {
        const convMessageIndex = state.currentConversation.messages.findIndex(m => m.id === messageId);
        if (convMessageIndex !== -1) {
          state.currentConversation.messages[convMessageIndex].content = content;
          
          // Atualizar na lista de conversas
          const conversationIndex = state.conversations.findIndex(
            c => c.sessionId === state.currentConversation!.sessionId
          );
          if (conversationIndex !== -1) {
            state.conversations[conversationIndex] = { ...state.currentConversation };
          }
        }
      }
    },
    
    removeMessage: (state, action: PayloadAction<string>) => {
      const messageId = action.payload;
      
      // Remover das mensagens atuais
      state.messages = state.messages.filter(m => m.id !== messageId);
      
      // Remover da conversa atual
      if (state.currentConversation) {
        state.currentConversation.messages = state.currentConversation.messages.filter(m => m.id !== messageId);
        state.currentConversation.messageCount = state.currentConversation.messages.length;
        
        // Atualizar na lista de conversas
        const conversationIndex = state.conversations.findIndex(
          c => c.sessionId === state.currentConversation!.sessionId
        );
        if (conversationIndex !== -1) {
          state.conversations[conversationIndex] = { ...state.currentConversation };
        }
      }
    },

    removeMessagesAfter: (state, action: PayloadAction<string>) => {
      const messageId = action.payload;
      
      // Encontrar o índice da mensagem editada
      const messageIndex = state.messages.findIndex(m => m.id === messageId);
      
      if (messageIndex !== -1) {
        // Manter apenas as mensagens até a mensagem editada (inclusive)
        state.messages = state.messages.slice(0, messageIndex + 1);
        
        // Atualizar na conversa atual
        if (state.currentConversation) {
          state.currentConversation.messages = [...state.messages];
          state.currentConversation.messageCount = state.messages.length;
          
          // Atualizar lastMessageAt com o timestamp da última mensagem restante
          if (state.messages.length > 0) {
            state.currentConversation.lastMessageAt = state.messages[state.messages.length - 1].timestamp;
            state.currentConversation.updatedAt = new Date().toISOString();
          }
          
          // Atualizar na lista de conversas
          const conversationIndex = state.conversations.findIndex(
            c => c.sessionId === state.currentConversation!.sessionId
          );
          if (conversationIndex !== -1) {
            state.conversations[conversationIndex] = { ...state.currentConversation };
          }
        }
      }
    },
    
    // Ações de UI
    setIsTyping: (state, action: PayloadAction<boolean>) => {
      state.isTyping = action.payload;
    },
    
    setStreamingMessage: (state, action: PayloadAction<string | null>) => {
      state.streamingMessage = action.payload;
      if (action.payload) {
        state.isTyping = false;
      }
    },
    
    clearError: (state) => {
      state.error = null;
    },
    
    // Ações de seleção
    setSelectedProvider: (state, action: PayloadAction<string>) => {
      state.selectedProvider = action.payload;
    },
    
    setSelectedModel: (state, action: PayloadAction<string>) => {
      state.selectedModel = action.payload;
    },
    
    // Ações de busca e filtros
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    
    setFilters: (state, action: PayloadAction<ConversationFilters>) => {
      state.filters = action.payload;
    },
    
    clearFilters: (state) => {
      state.filters = {};
      state.searchQuery = '';
    },
    
    toggleFavorite: (state, action: PayloadAction<string>) => {
      const conversationId = action.payload;
      const index = state.favoriteConversations.indexOf(conversationId);
      
      if (index > -1) {
        state.favoriteConversations.splice(index, 1);
      } else {
        state.favoriteConversations.push(conversationId);
      }
      
      // Atualizar na lista de conversas
      const conversation = state.conversations.find(c => c.sessionId === conversationId);
      if (conversation) {
        conversation.isFavorite = index === -1;
        
        // Atualizar conversa atual se for a mesma
        if (state.currentConversation?.sessionId === conversationId) {
          state.currentConversation.isFavorite = conversation.isFavorite;
        }
      }
    },
    
    // Ações de sessão
    setPendingSessionId: (state, action: PayloadAction<string | null>) => {
      state.pendingSessionId = action.payload;
    },
    
    applyPendingSessionId: (state) => {
      if (state.pendingSessionId) {
        // Criar nova conversa ou atualizar existente
        if (!state.currentConversation) {
          state.currentConversation = {
            id: state.pendingSessionId,
            sessionId: state.pendingSessionId,
            title: 'Nova conversa',
            messages: [...state.messages],
            provider: state.selectedProvider,
            model: state.selectedModel,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            isFavorite: false,
            tags: [],
            totalTokens: 0,
            totalCost: 0,
            lastMessageAt: new Date().toISOString(),
            messageCount: state.messages.length
          };

          // Adicionar à lista de conversas
          state.conversations.unshift(state.currentConversation);
        } else {
          // Atualizar IDs da conversa existente
          state.currentConversation.id = state.pendingSessionId;
          state.currentConversation.sessionId = state.pendingSessionId;

          // Atualizar messages com o conversation ID correto
          state.messages.forEach(message => {
            message.conversationId = state.pendingSessionId!;
          });
          state.currentConversation.messages = [...state.messages];

          // Atualizar na lista de conversas se já existir
          const existingIndex = state.conversations.findIndex(c => c.id === state.currentConversation!.id);
          if (existingIndex !== -1) {
            state.conversations[existingIndex] = { ...state.currentConversation };
          } else {
            // Se não existe na lista, adicionar no início
            state.conversations.unshift(state.currentConversation);
          }
        }

        state.pendingSessionId = null;
      }
    },

    // Nova ação para adicionar conversa ao histórico imediatamente
    addConversationToHistory: (state, action: PayloadAction<Conversation>) => {
      const newConversation = action.payload;

      // Verificar se a conversa já existe na lista
      const existingIndex = state.conversations.findIndex(c => c.sessionId === newConversation.sessionId);

      if (existingIndex === -1) {
        // Se não existe, adicionar no início da lista
        state.conversations.unshift(newConversation);
      } else {
        // Se já existe, atualizar
        state.conversations[existingIndex] = newConversation;
      }
    },
  },
  
  extraReducers: (builder) => {
    // Send Message
    builder
      .addCase(sendMessage.pending, (state) => {
        state.isLoading = true;
        state.isTyping = true;
        state.error = null;
      })
      .addCase(sendMessage.fulfilled, (state, _action) => {
        state.isLoading = false;
        state.isTyping = false;
        // Não adicionar a mensagem aqui, será adicionada via streaming
      })
      .addCase(sendMessage.rejected, (state, action) => {
        state.isLoading = false;
        state.isTyping = false;
        state.error = action.payload || 'Failed to send message';
      });
    
    // Upload File
    builder
      .addCase(uploadFile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(uploadFile.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(uploadFile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || 'Failed to upload file';
      });
    
    // Fetch Conversations
    builder
      .addCase(fetchConversations.pending, (state) => {
        if (state.conversations.length === 0 && !state.currentConversation) 
          state.isLoading = true;
        
      })
      .addCase(fetchConversations.fulfilled, (state, action) => {
        state.isLoading = false;
        const newConversations = action.payload.conversations;

        // Se há uma conversa atual que não está na lista retornada, preservá-la
        if (state.currentConversation &&
            state.currentConversation.sessionId &&
            !newConversations.find(c => c.sessionId === state.currentConversation!.sessionId)) {
          // Adicionar a conversa atual no início da lista se ela não estiver presente
          state.conversations = [state.currentConversation, ...newConversations];
        } else {
          state.conversations = newConversations;
        }

        state.hasMore = action.payload.hasMore;
      })
      .addCase(fetchConversations.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || 'Failed to fetch conversations';
      });
    
    // Fetch Single Conversation
    builder
      .addCase(fetchConversation.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchConversation.fulfilled, (state, action) => {
        state.isLoading = false;
        const conversation = action.payload;
        
        // Adicionar ou atualizar na lista
        const existingIndex = state.conversations.findIndex(c => c.sessionId === conversation.sessionId);
        if (existingIndex > -1) {
          state.conversations[existingIndex] = conversation;
        } else {
          state.conversations.unshift(conversation);
        }
      })
      .addCase(fetchConversation.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || 'Failed to fetch conversation';
      });
    
    // Delete Conversation
    builder
      .addCase(deleteConversation.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteConversation.fulfilled, (state, action) => {
        state.isLoading = false;
        const deletedId = action.payload;
        
        // Remover da lista
        state.conversations = state.conversations.filter(c => c.sessionId !== deletedId);
        state.favoriteConversations = state.favoriteConversations.filter(id => id !== deletedId);
        
        // Se era a conversa atual, limpar
        if (state.currentConversation?.sessionId === deletedId) {
          state.currentConversation = null;
          state.messages = [];
          state.streamingMessage = null;
          state.isTyping = false;
        }
      })
      .addCase(deleteConversation.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || 'Failed to delete conversation';
      });
    
    // Update Conversation Title
    builder
      .addCase(updateConversationTitle.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateConversationTitle.fulfilled, (state, action) => {
        state.isLoading = false;
        const { id, title } = action.payload;
        
        // Atualizar na lista
        const conversationIndex = state.conversations.findIndex(c => c.sessionId === id);
        if (conversationIndex > -1) {
          state.conversations[conversationIndex].title = title;
        }
        
        // Atualizar conversa atual se for a mesma
        if (state.currentConversation?.sessionId === id) {
          state.currentConversation.title = title;
        }
      })
      .addCase(updateConversationTitle.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || 'Failed to update conversation title';
      });
  },
});

export const {
  setCurrentConversation,
  startNewConversation,
  addMessage,
  updateMessage,
  removeMessage,
  removeMessagesAfter,
  setIsTyping,
  setStreamingMessage,
  clearError,
  setSelectedProvider,
  setSelectedModel,
  setSearchQuery,
  setFilters,
  clearFilters,
  toggleFavorite,
  setPendingSessionId,
  applyPendingSessionId,
  addConversationToHistory,
} = chatConversationsSlice.actions;

export const chatConversationsReducer = chatConversationsSlice.reducer;
