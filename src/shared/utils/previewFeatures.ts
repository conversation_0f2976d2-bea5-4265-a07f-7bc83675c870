import { config } from '../config';

/**
 * Utilitários para preview features
 */
export const previewFeatures = {
  /**
   * Verifica se preview features estão habilitadas globalmente
   */
  isEnabled: () => config.ALLOW_PREVIEW_FEATURES,

  /**
   * Lista de features disponíveis (para documentação/debug)
   */
  availableFeatures: [
    'workflows',
    'knowledge-base',
    'tools-dropdown',
    'file-upload',
    'advanced-settings',
    'new-ui-components', 
    'experimental-ai-features',
    'beta-integrations',
    'debug-tools'
  ] as const,

  /**
   * Verifica se uma feature específica está habilitada
   */
  isFeatureEnabled: (featureName: string) => {
    const enabled = config.ALLOW_PREVIEW_FEATURES;
    
    if (enabled && import.meta.env.DEV) {
      console.log(`🚀 Preview feature "${featureName}" is enabled`);
    }
    
    return enabled;
  },

  /**
   * Wrapper para executar código apenas se preview features estiverem habilitadas
   */
  withPreview: <T>(callback: () => T, fallback?: T): T | undefined => {
    return config.ALLOW_PREVIEW_FEATURES ? callback() : fallback;
  },

  /**
   * Classe CSS condicional para preview features
   */
  previewClass: (className: string, fallbackClass?: string) => {
    return config.ALLOW_PREVIEW_FEATURES ? className : (fallbackClass || '');
  }
};

/**
 * Type para features disponíveis
 */
export type PreviewFeatureName = typeof previewFeatures.availableFeatures[number];
