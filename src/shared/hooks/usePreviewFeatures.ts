import React from 'react';
import { config } from '../config';

/**
 * Hook para verificar se preview features estão habilitadas
 * Por padrão, preview features estão desabilitadas (produção)
 * Podem ser habilitadas via VITE_ALLOW_PREVIEW_FEATURES=true no .env
 */
export const usePreviewFeatures = () => {
  return {
    isEnabled: config.ALLOW_PREVIEW_FEATURES,
    
    /**
     * Verifica se uma feature específica está habilitada
     * @param featureName Nome da feature (para logs/debug)
     */
    isFeatureEnabled: (featureName?: string) => {
      if (featureName && config.ALLOW_PREVIEW_FEATURES) {
        console.log(`🚀 Preview feature "${featureName}" is enabled`);
      }
      return config.ALLOW_PREVIEW_FEATURES;
    }
  };
};

/**
 * Componente wrapper para renderizar conteúdo apenas se preview features estiverem habilitadas
 */
interface PreviewFeatureProps {
  children: React.ReactNode;
  featureName?: string;
}

export const PreviewFeature: React.FC<PreviewFeatureProps> = ({ children, featureName }) => {
  const { isFeatureEnabled } = usePreviewFeatures();
  
  if (!isFeatureEnabled(featureName)) {
    return null;
  }
  
  return React.createElement(React.Fragment, null, children);
};
