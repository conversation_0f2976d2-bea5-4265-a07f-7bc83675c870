import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { GoogleLoginButton } from '../GoogleLoginButton';

describe('GoogleLoginButton', () => {
  const mockOnLogin = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should render with default text', () => {
    render(<GoogleLoginButton onLogin={mockOnLogin} />);
    
    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('Entrar com Google');
  });

  it('should call onLogin when clicked', () => {
    render(<GoogleLoginButton onLogin={mockOnLogin} />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    expect(mockOnLogin).toHaveBeenCalledTimes(1);
  });

  it('should show loading state when clicked', async () => {
    const slowOnLogin = vi.fn(() => new Promise(resolve => setTimeout(resolve, 1000)));
    render(<GoogleLoginButton onLogin={slowOnLogin} />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    // Should show loading state immediately
    expect(button).toHaveTextContent('Conectando...');
    expect(button).toBeDisabled();
    expect(button).toHaveClass('disabled:opacity-70', 'disabled:cursor-not-allowed');
    
    // Should show spinner
    const spinner = button.querySelector('.animate-spin');
    expect(spinner).toBeInTheDocument();
  });

  it('should prevent multiple clicks while loading', async () => {
    const slowOnLogin = vi.fn(() => new Promise(resolve => setTimeout(resolve, 1000)));
    render(<GoogleLoginButton onLogin={slowOnLogin} />);
    
    const button = screen.getByRole('button');
    
    // First click
    fireEvent.click(button);
    expect(slowOnLogin).toHaveBeenCalledTimes(1);
    
    // Second click while loading - should be ignored
    fireEvent.click(button);
    expect(slowOnLogin).toHaveBeenCalledTimes(1);
  });

  it('should return to normal state after loading completes', async () => {
    vi.useRealTimers(); // Use real timers for this test
    const fastOnLogin = vi.fn(() => Promise.resolve());
    render(<GoogleLoginButton onLogin={fastOnLogin} />);

    const button = screen.getByRole('button');
    fireEvent.click(button);

    // Should be loading initially
    expect(button).toHaveTextContent('Conectando...');
    expect(button).toBeDisabled();

    // Wait for the minimum loading time (500ms) + some buffer
    await waitFor(() => {
      expect(button).toHaveTextContent('Entrar com Google');
      expect(button).not.toBeDisabled();
    }, { timeout: 1000 });

    vi.useFakeTimers(); // Restore fake timers
  });

  it('should handle login errors gracefully', async () => {
    vi.useRealTimers(); // Use real timers for this test
    const errorOnLogin = vi.fn(() => Promise.reject(new Error('Login failed')));
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    render(<GoogleLoginButton onLogin={errorOnLogin} />);

    const button = screen.getByRole('button');
    fireEvent.click(button);

    // Should show loading state
    expect(button).toHaveTextContent('Conectando...');

    // Wait for error handling and minimum loading time
    await waitFor(() => {
      expect(button).toHaveTextContent('Entrar com Google');
      expect(button).not.toBeDisabled();
    }, { timeout: 1000 });

    expect(consoleSpy).toHaveBeenCalledWith('Google login error:', expect.any(Error));
    consoleSpy.mockRestore();
    vi.useFakeTimers(); // Restore fake timers
  });

  it('should apply custom className', () => {
    render(<GoogleLoginButton onLogin={mockOnLogin} className="custom-class" />);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('custom-class');
  });

  it('should have proper accessibility attributes', () => {
    render(<GoogleLoginButton onLogin={mockOnLogin} />);

    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
    expect(button.tagName).toBe('BUTTON');
  });

  it('should maintain visual styling during loading', async () => {
    const slowOnLogin = vi.fn(() => new Promise(resolve => setTimeout(resolve, 1000)));
    render(<GoogleLoginButton onLogin={slowOnLogin} />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    // Should maintain gradient background and other styling
    expect(button).toHaveClass('bg-gradient-to-r', 'from-orange-500', 'to-red-500');
    expect(button).toHaveClass('rounded-2xl', 'shadow-lg');
  });
});
