import React from 'react';
import { AlertTriangle, Trash2 } from 'lucide-react';
import { Button } from '../atoms/Button';

export interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'danger' | 'warning' | 'info';
  loading?: boolean;
  icon?: React.ReactNode;
}

export const ConfirmationModal = React.memo<ConfirmationModalProps>(
  ({
    isOpen,
    onClose,
    onConfirm,
    title,
    message,
    confirmText = 'Confirmar',
    cancelText = 'Cancelar',
    type = 'danger',
    loading = false,
    icon,
  }) => {
    const typeConfig = {
      danger: {
        icon: icon || <Trash2 className="w-6 h-6" />,
        iconBgColor: 'bg-red-50 border border-red-200',
        iconColor: 'text-red-600',
        confirmButtonClass: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-200',
      },
      warning: {
        icon: icon || <AlertTriangle className="w-6 h-6" />,
        iconBgColor: 'bg-yellow-50 border border-yellow-200',
        iconColor: 'text-yellow-600',
        confirmButtonClass: 'bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-200',
      },
      info: {
        icon: icon || <AlertTriangle className="w-6 h-6" />,
        iconBgColor: 'bg-blue-50 border border-blue-200',
        iconColor: 'text-blue-600',
        confirmButtonClass: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-200',
      },
    };

    const config = typeConfig[type];

    const handleConfirm = () => {
      onConfirm();
      if (!loading) {
        onClose();
      }
    };

    if (!isOpen) return null;

    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        {/* Backdrop */}
        <div
          className="absolute inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-200"
          onClick={!loading ? onClose : undefined}
        />

        {/* Modal */}
        <div className="relative bg-white rounded-2xl shadow-xl max-w-md w-full transform transition-all duration-200 scale-100 animate-in fade-in-0 zoom-in-95 border border-gray-100">
          <div className="p-6">
            {/* Icon */}
            <div className="flex items-center justify-center mb-6">
              <div className={`flex h-16 w-16 items-center justify-center rounded-full ${config.iconBgColor} shadow-md`}>
                <div className={`${config.iconColor}`}>
                  {config.icon}
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="text-center mb-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {title}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {message}
              </p>
            </div>

            {/* Actions */}
            <div className="flex gap-3">
              <Button
                variant="outline"
                size="md"
                onClick={onClose}
                disabled={loading}
                className="flex-1 border-gray-300 text-gray-700 hover:bg-gray-50 transition-colors duration-150"
              >
                {cancelText}
              </Button>
              <Button
                variant="primary"
                size="md"
                onClick={handleConfirm}
                loading={loading}
                disabled={loading}
                className={`flex-1 transition-colors duration-150 ${config.confirmButtonClass}`}
              >
                {confirmText}
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }
);

ConfirmationModal.displayName = 'ConfirmationModal';
