import React from 'react';
import { Sparkles } from 'lucide-react';
import cognitLogo from '../../assets/cognit-logo.svg';
import styles from '../../styles/components/molecules/LoginHeader.module.css';

interface LoginHeaderProps {
  className?: string;
}

export const LoginHeader: React.FC<LoginHeaderProps> = ({ className = '' }) => {
  return (
    <div className={`text-center mb-12 ${className}`}>
      {/* Main Logo */}
      <div className="flex items-center justify-center space-x-4 mb-6">
        <img 
          src={cognitLogo} 
          alt="Cognit Logo" 
          className={styles.logo}
        />
      </div>

      {/* Subtitle */}
      {/* <p className="text-xl text-gray-700 font-medium mb-2">
        Knowledge Base Platform
      </p> */}
      
      {/* Feature Badges */}
      <div className="flex items-center justify-center space-x-2 mt-3">
        <div className="flex items-center space-x-1 px-3 py-1 bg-gradient-to-r from-orange-100 to-red-100 rounded-full border border-orange-200/50">
          <Sparkles className="w-4 h-4 text-orange-500" />
          <span className="text-sm font-medium text-orange-700">Powered by AI</span>
        </div>
      </div>

      {/* Decorative Line */}
      <div className="flex items-center justify-center mt-8 mb-8">
        <div className="flex-1 h-px bg-gradient-to-r from-transparent via-orange-300 to-transparent"></div>
        <div className="w-2 h-2 bg-gradient-to-r from-orange-400 to-red-500 rounded-full mx-4"></div>
        <div className="flex-1 h-px bg-gradient-to-r from-transparent via-orange-300 to-transparent"></div>
      </div>
    </div>
  );
};
