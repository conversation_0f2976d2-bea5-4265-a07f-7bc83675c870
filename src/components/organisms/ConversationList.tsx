import React from 'react';
import { SearchBar } from '../molecules/SearchBar';
import { Card, Input, Icon, Badge } from '../atoms';
import { ConfirmationModal } from '../molecules/ConfirmationModal';
import { useConfirmation } from '../../hooks/useConfirmation';
import { useToast } from '../../hooks/useToast';
import '../../styles/components/organisms/conversation-list.css';

export interface Conversation {
  id: string;
  title: string;
  lastMessage?: string;
  timestamp: Date;
  model?: string;
  provider?: string;
  isFavorite?: boolean;
  messageCount?: number;
}

export interface ConversationListProps {
  conversations: Conversation[];
  selectedConversationId?: string;
  onConversationSelect: (conversationId: string) => void;
  onConversationDelete?: (conversationId: string) => void;
  onConversationRename?: (conversationId: string, newTitle: string) => void;
  onConversationToggleFavorite?: (conversationId: string) => void;
  onSearchChange?: (query: string) => void;
  searchQuery?: string;
  isLoading?: boolean;
  className?: string;
  showSearch?: boolean;
}

export const ConversationList: React.FC<ConversationListProps> = ({
  conversations,
  selectedConversationId,
  onConversationSelect,
  onConversationDelete,
  onConversationRename,
  onSearchChange,
  searchQuery = '',
  isLoading = false,
  className = '',
  showSearch = true,
}) => {
  const [editingId, setEditingId] = React.useState<string | null>(null);
  const [editingTitle, setEditingTitle] = React.useState('');
  const { confirmationState, showConfirmation, hideConfirmation, handleConfirm } = useConfirmation();
  const { showSuccess, showError } = useToast();

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diffInHours = (now.getTime() - timestamp.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Agora há pouco';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h atrás`;
    } else if (diffInHours < 168) { // 7 days
      return `${Math.floor(diffInHours / 24)}d atrás`;
    } else {
      return timestamp.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
      });
    }
  };

  const handleStartEdit = (conversation: Conversation) => {
    setEditingId(conversation.id);
    setEditingTitle(conversation.title);
  };

  const handleSaveEdit = () => {
    if (editingId && editingTitle.trim()) {
      onConversationRename?.(editingId, editingTitle.trim());
    }
    setEditingId(null);
    setEditingTitle('');
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setEditingTitle('');
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      handleCancelEdit();
    }
  };

  // Group conversations by favorites and recent
  const favoriteConversations = conversations.filter(conv => conv.isFavorite);
  const regularConversations = conversations.filter(conv => !conv.isFavorite);

  const renderConversation = (conversation: Conversation) => {
    const isSelected = conversation.id === selectedConversationId;
    const isEditing = editingId === conversation.id;

    return (
      <Card
        key={conversation.id}
        variant={isSelected ? 'outlined' : 'default'}
        className={`
          conversation-card group relative cursor-pointer transition-colors
          ${isSelected
            ? 'bg-orange-50 border-orange-200'
            : 'hover:bg-orange-50 hover:border-orange-200'
          }
        `}
        onClick={() => !isEditing && onConversationSelect(conversation.id)}
      >
        <div className="conversation-card-content">
          <div className="conversation-card-main">
            {isEditing ? (
              <Input
                type="text"
                value={editingTitle}
                onChange={(e) => setEditingTitle(e.target.value)}
                onBlur={handleSaveEdit}
                onKeyDown={handleKeyDown}
                size="sm"
                className="text-sm font-medium"
                autoFocus
              />
            ) : (
              <>
                <div className="conversation-title-row">
                  <div className="conversation-title-content">
                    <div className="flex items-center gap-2">
                      <Icon name="message-circle" size="sm" className="text-neutral-400 flex-shrink-0" />
                      <h3 className="text-sm font-medium text-neutral-900 truncate">
                        {conversation.title}
                      </h3>
                      {conversation.model && (
                        <Badge variant="primary" size="sm" className="ml-auto">
                          {conversation.model.toUpperCase()}
                        </Badge>
                      )}
                      {conversation.isFavorite && (
                        <Icon name="star" className="w-3 h-3 text-warning fill-current flex-shrink-0" />
                      )}
                    </div>
                  </div>

                  {/* Botões de ação diretos */}
                  <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <div
                      onClick={(e) => {
                        e.stopPropagation();
                        handleStartEdit(conversation);
                      }}
                      className="p-1 text-gray-500 hover:text-orange-700 hover:bg-orange-100 rounded cursor-pointer transition-colors"
                      title="Renomear conversa"
                    >
                      <Icon name="edit" size="sm" />
                    </div>

                    <div
                      onClick={(e) => {
                        e.stopPropagation();
                        showConfirmation(
                          {
                            title: 'Excluir conversa',
                            message: `Tem certeza que deseja excluir a conversa "${conversation.title}"? Esta ação não pode ser desfeita.`,
                            confirmText: 'Excluir',
                            cancelText: 'Cancelar',
                            type: 'danger',
                          },
                          async () => {
                            try {
                              await onConversationDelete?.(conversation.id);
                              showSuccess('Conversa excluída', `A conversa "${conversation.title}" foi removida com sucesso.`);
                            } catch (error) {
                              showError('Erro ao excluir', 'Não foi possível excluir a conversa. Tente novamente.');
                              throw error;
                            }
                          }
                        );
                      }}
                      className="p-1 text-gray-500 hover:text-red-700 hover:bg-red-100 rounded cursor-pointer transition-colors"
                      title="Excluir conversa"
                    >
                      <Icon name="trash-2" size="sm" />
                    </div>
                  </div>
                </div>

                {/* Quantidade de mensagens e tempo */}
                <div className="flex items-center gap-2 mt-1">
                  {conversation.messageCount && (
                    <span className="text-xs text-neutral-400">
                      {conversation.messageCount} msgs
                    </span>
                  )}
                  <span className="text-xs text-neutral-400">
                    {formatTimestamp(conversation.timestamp)}
                  </span>
                </div>

                {conversation.lastMessage && (
                  <p className="conversation-summary text-xs text-neutral-500 truncate">
                    {conversation.lastMessage}
                  </p>
                )}
              </>
            )}
          </div>
        </div>
      </Card>
    );
  };

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Search */}
      {showSearch && (
        <div className="p-4 border-b border-neutral-200">
          <SearchBar
            value={searchQuery}
            onChange={onSearchChange}
            placeholder="Buscar conversas..."
            size="md"
          />
        </div>
      )}

      {/* Conversations List */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="p-4 text-center text-neutral-500">
            <p>Carregando conversas...</p>
          </div>
        ) : conversations.length === 0 ? (
          <div className="p-4 text-center text-neutral-500">
            <Icon name="message-square" className="w-12 h-12 mx-auto mb-3 text-neutral-300" />
            <p className="text-sm">
              {searchQuery ? 'Nenhuma conversa encontrada' : 'Nenhuma conversa ainda'}
            </p>
          </div>
        ) : (
          <div className="conversation-list-container">
            {/* Favorites Section */}
            {favoriteConversations.length > 0 && (
              <div className="mb-4">
                <h4 className="text-xs font-medium text-neutral-500 uppercase tracking-wide px-2 mb-2">
                  Favoritos
                </h4>
                <div>
                  {favoriteConversations.map(renderConversation)}
                </div>
              </div>
            )}

            {/* Regular Conversations */}
            {regularConversations.length > 0 && (
              <div>
                {favoriteConversations.length > 0 && (
                  <h4 className="text-xs font-medium text-neutral-500 uppercase tracking-wide px-2 mb-2">
                    Recentes
                  </h4>
                )}
                <div>
                  {regularConversations.map(renderConversation)}
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmationState.isOpen}
        onClose={hideConfirmation}
        onConfirm={handleConfirm}
        title={confirmationState.title}
        message={confirmationState.message}
        confirmText={confirmationState.confirmText}
        cancelText={confirmationState.cancelText}
        type={confirmationState.type}
        loading={confirmationState.loading}
        icon={confirmationState.icon}
      />
    </div>
  );
};

export default ConversationList;