import React, { useState } from 'react';
import { Plus, ChevronLeft, Search } from 'lucide-react';
import { ConversationList } from './ConversationList';
import type { Conversation } from './ConversationList';
import '../../styles/components/organisms/scrollbar.css';
import '../../styles/components/organisms/studio-sidebar.css';

export interface StudioSidebarProps {
  conversations: Conversation[];
  currentConversationId?: string;
  onConversationSelect: (conversationId: string) => void;
  onConversationDelete?: (conversationId: string) => void;
  onConversationRename?: (conversationId: string, newTitle: string) => void;
  onNewConversation: () => void;
  isCollapsed?: boolean;
  onToggleCollapse: () => void;
  isLoading?: boolean;
}

export const StudioSidebar: React.FC<StudioSidebarProps> = ({
  conversations,
  currentConversationId,
  onConversationSelect,
  onConversationDelete,
  onConversationRename,
  onNewConversation,
  isCollapsed = false,
  onToggleCollapse,
  isLoading = false,
}) => {
  const [searchQuery, setSearchQuery] = useState('');

  // Filtrar conversas baseado na busca
  const filteredConversations = conversations.filter(conv =>
    conv.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    conv.lastMessage?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div
      className={`
        studio-sidebar border-r border-gray-200 flex flex-col h-full transition-all duration-300 ease-in-out
        ${isCollapsed ? 'w-0 border-r-0 overflow-visible' : 'w-80'}
      `}
    >
      {/* Botão flutuante de toggle - apenas quando expandida */}
      {!isCollapsed && (
        <button
          onClick={onToggleCollapse}
          className="sidebar-toggle-button"
          title="Recolher histórico"
        >
          <ChevronLeft />
        </button>
      )}

      {!isCollapsed && (
        <>
          {/* Campo de busca */}
          <div className="p-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Buscar conversas..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Lista de conversas com scrollbar customizada */}
          <div className="flex-1 overflow-y-auto conversation-list-scroll">
            <ConversationList
              conversations={filteredConversations}
              selectedConversationId={currentConversationId}
              onConversationSelect={onConversationSelect}
              onConversationDelete={onConversationDelete}
              onConversationRename={onConversationRename}
              onSearchChange={setSearchQuery}
              searchQuery={searchQuery}
              isLoading={isLoading}
              showSearch={false} // Já temos o campo de busca acima
              className="p-0"
            />
          </div>

          {/* Footer com botão Nova Conversa */}
          <div className="p-3">
            <button
              onClick={onNewConversation}
              className="w-full flex items-center justify-center gap-2 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white py-2.5 px-4 rounded-lg font-medium transition-all duration-200 hover:shadow-lg"
            >
              <Plus className="w-4 h-4" />
              Nova conversa
            </button>
          </div>
        </>
      )}

      {/* Estado colapsado - apenas botão de nova conversa */}
      {isCollapsed && (
        <div className="flex flex-col items-center py-4">
          <button
            onClick={onNewConversation}
            className="p-2 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white rounded-lg transition-all duration-200 hover:shadow-lg"
            title="Nova conversa"
          >
            <Plus className="w-4 h-4" />
          </button>
        </div>
      )}
    </div>
  );
};
