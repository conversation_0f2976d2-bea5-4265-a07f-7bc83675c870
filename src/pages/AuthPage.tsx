import React from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { LoginTemplate } from '../components/templates';
import { useAuth } from '../hooks/useAuth';
import { setUser } from '../redux/auth/authReducer';
import { logger } from '../shared/utils/logger';
import { shouldUseMockServer } from '../api/mockServer';

export const AuthPage: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { login, initiateGoogleLogin, loginWithGoogle } = useAuth();

  const handleLogin = async (email: string, password: string) => {
    try {
      const credentials = { email, password };
      await login(credentials);
    } catch (error) {
      logger.error('Login failed:', error);
      throw new Error('Login failed');
    }
  };

  const handleGoogleLogin = async () => {
    try {
      if (shouldUseMockServer()) {
        // Usar fluxo mock existente com token dummy
        const result = await loginWithGoogle('mock-google-token-from-auth-page');
        
        if (result) {
          // Navigate to studio (tokens já foram salvos pela função loginWithGoogle)
          navigate('/studio');
        }
      } else {
        // Usar API real - iniciar fluxo OAuth
        await initiateGoogleLogin();
        // O redirecionamento será feito pelo backend
      }
    } catch (error) {
      logger.error('Google login failed:', error);
    }
  };

  return (
    <LoginTemplate onLogin={handleLogin} onGoogleLogin={handleGoogleLogin} />
  );
};
