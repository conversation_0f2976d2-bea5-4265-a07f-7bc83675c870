/* Estilos para a sidebar do Studio */

.studio-sidebar {
  background-color: #F8F9FA;
  position: relative;
}

/* Botão flutuante de toggle do histórico */
.sidebar-toggle-button {
  position: absolute;
  top: 50%;
  right: -16px; /* Ajustado para o novo tamanho */
  transform: translateY(-50%);
  width: 32px;
  height: 32px;
  background: white;
  border: 1px solid #E5E7EB;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  z-index: 50;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.sidebar-toggle-button:hover {
  background: #F3F4F6;
  border-color: #D1D5DB;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.sidebar-toggle-button:active {
  transform: translateY(-50%) scale(0.95);
}

/* Ícone dentro do botão */
.sidebar-toggle-button svg {
  width: 16px;
  height: 16px;
  color: #6B7280;
  transition: transform 0.2s ease-in-out;
}

.sidebar-toggle-button:hover svg {
  color: #374151;
}

/* Rotação do ícone baseado no estado */
.sidebar-toggle-button.collapsed svg {
  transform: rotate(180deg);
}

/* Botão de Nova Conversa quando sidebar está colapsada */
.sidebar-new-conversation-button {
  position: absolute;
  top: 50%;
  right: -16px;
  transform: translateY(calc(-50% + 50px)); /* Posiciona abaixo do botão de toggle */
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #f97316, #dc2626);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  z-index: 50;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.sidebar-new-conversation-button:hover {
  background: linear-gradient(135deg, #ea580c, #b91c1c);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transform: translateY(calc(-50% + 50px)) scale(1.05);
}

.sidebar-new-conversation-button svg {
  width: 16px;
  height: 16px;
  color: white;
}
