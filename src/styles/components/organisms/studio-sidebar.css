/* Estilos para a sidebar do Studio */

.studio-sidebar {
  background-color: #F8F9FA;
  position: relative;
}

/* Botão flutuante de toggle do histórico */
.sidebar-toggle-button {
  position: absolute;
  top: 50%;
  right: -16px; /* Ajustado para o novo tamanho */
  transform: translateY(-50%);
  width: 32px;
  height: 32px;
  background: white;
  border: 1px solid #E5E7EB;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  z-index: 50;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.sidebar-toggle-button:hover {
  background: #F3F4F6;
  border-color: #D1D5DB;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.sidebar-toggle-button:active {
  transform: translateY(-50%) scale(0.95);
}

/* Ícone dentro do botão */
.sidebar-toggle-button svg {
  width: 16px;
  height: 16px;
  color: #6B7280;
  transition: transform 0.2s ease-in-out;
}

.sidebar-toggle-button:hover svg {
  color: #374151;
}

/* Botão de toggle quando sidebar está expandida */
.sidebar-toggle-button {
  /* Mantém apenas os estilos básicos para quando sidebar está expandida */
}
