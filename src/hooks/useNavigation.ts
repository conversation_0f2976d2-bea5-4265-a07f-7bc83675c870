import { useLocation } from 'react-router-dom';
import { Sparkles, Workflow } from 'lucide-react';
import type { LucideIcon } from 'lucide-react';
import { config } from '../shared/config';

export interface NavigationItem {
  id: string;
  label: string;
  icon: LucideIcon;
  description: string;
  fullDescription?: string;
  badge?: string;
  gradient?: boolean;
  path: string;
}

export const useNavigation = () => {
  const location = useLocation();

  const baseNavigationItems: NavigationItem[] = [
    {
      id: 'studio',
      label: 'Studio',
      icon: Sparkles,
      description: 'Cognit Studio',
      fullDescription: 'Assistente de IA do IEBT',
      path: '/studio',
    },
  ];

  // Adiciona Workflows apenas se preview features estiverem habilitadas
  const workflowsItem: NavigationItem = {
    id: 'workflows',
    label: 'Workflows',
    icon: Workflow,
    description: 'Visual Workflows',
    fullDescription: 'Editor visual de workflows com n8n',
    path: '/workflows',
  };

  const navigationItems: NavigationItem[] = config.ALLOW_PREVIEW_FEATURES
    ? [...baseNavigationItems, workflowsItem]
    : baseNavigationItems;

  // Log para debug em desenvolvimento
  if (config.ALLOW_PREVIEW_FEATURES && import.meta.env.DEV) {
    console.log('🚀 Preview feature "workflows" is enabled - Workflows tab visible');
  }

  const isActive = (path: string): boolean => {
    if (path === '/studio') {
      return location.pathname === '/' || location.pathname === '/studio';
    }
    return location.pathname.startsWith(path);
  };

  const getCurrentView = (): string => {
    const currentPath = location.pathname;
    if (currentPath === '/' || currentPath.startsWith('/studio')) return 'studio';
    if (currentPath.startsWith('/workflows')) return 'workflows';
    return 'studio';
  };

  return {
    navigationItems,
    isActive,
    getCurrentView,
    currentPath: location.pathname,
  };
};
