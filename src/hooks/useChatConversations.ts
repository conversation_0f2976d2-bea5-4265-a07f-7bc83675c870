import { useCallback } from 'react';
import { useAppDispatch, useAppSelector, store } from '../redux/store';
import {
  sendMessage,
  uploadFile,
  fetchConversations,
  fetchConversation,
  deleteConversation,
  updateConversationTitle,
  searchConversations
} from '../redux/chatConversations/chatConversationsActions';
import {
  setCurrentConversation,
  startNewConversation,
  addMessage,
  updateMessage,
  removeMessage,
  removeMessagesAfter,
  setIsTyping,
  setStreamingMessage,
  clearError,
  setSelectedProvider,
  setSelectedModel,
  setSearchQuery,
  setFilters,
  clearFilters,
  toggleFavorite,
  setPendingSessionId,
  applyPendingSessionId,
} from '../redux/chatConversations/chatConversationsReducer';
import {
  selectConversations,
  selectCurrentConversation,
  selectMessages,
  selectIsLoading,
  selectIsTyping,
  selectStreamingMessage,
  selectError,
  selectSelectedProvider,
  selectSelectedModel,
  selectSearchQuery,
  selectFilters,
  selectFavoriteConversations,
  selectHasMore,
  selectFilteredConversations,
  selectFavoriteConversationsList,
  selectRecentConversations,
  selectConversationsByProvider,
  selectConversationById,
  selectLastMessage,
  selectMessageCount,
  selectPendingSessionId,
  selectHasMessages,
  selectCanRegenerate,
  selectIsStreaming,
  selectHasActiveFilters,
} from '../redux/chatConversations/chatConversationsSelectors';
import type {
  SendMessageRequest,
  Conversation,
  Message,
  ConversationFilters
} from '../types/chat';

/**
 * Hook unificado para gerenciar chat e conversas
 * Substitui os hooks useChat e useConversations
 */
export const useChatConversations = () => {
  const dispatch = useAppDispatch();
  
  // === SELECTORS ===
  
  // Conversas
  const conversations = useAppSelector(selectConversations);
  const currentConversation = useAppSelector(selectCurrentConversation);
  const filteredConversations = useAppSelector(selectFilteredConversations);
  const favoriteConversationsList = useAppSelector(selectFavoriteConversationsList);
  const recentConversations = useAppSelector(selectRecentConversations);
  const conversationsByProvider = useAppSelector(selectConversationsByProvider);
  
  // Mensagens
  const messages = useAppSelector(selectMessages);
  const lastMessage = useAppSelector(selectLastMessage);
  const messageCount = useAppSelector(selectMessageCount);
  
  // Estado de UI
  const isLoading = useAppSelector(selectIsLoading);
  const isTyping = useAppSelector(selectIsTyping);
  const streamingMessage = useAppSelector(selectStreamingMessage);
  const error = useAppSelector(selectError);
  
  // Seleções do usuário
  const selectedProvider = useAppSelector(selectSelectedProvider);
  const selectedModel = useAppSelector(selectSelectedModel);
  
  // Busca e filtros
  const searchQuery = useAppSelector(selectSearchQuery);
  const filters = useAppSelector(selectFilters);
  const favoriteConversations = useAppSelector(selectFavoriteConversations);
  const hasMore = useAppSelector(selectHasMore);
  
  // Estado temporário
  const pendingSessionId = useAppSelector(selectPendingSessionId);
  
  // Computados
  const hasMessages = useAppSelector(selectHasMessages);
  const canRegenerate = useAppSelector(selectCanRegenerate);
  const isStreaming = useAppSelector(selectIsStreaming);
  const hasActiveFilters = useAppSelector(selectHasActiveFilters);
  
  // === AÇÕES DE CONVERSA ===
  
  const loadConversations = useCallback((
    options: { limit?: number; offset?: number; search?: string } = {}
  ) => {
    return dispatch(fetchConversations(options));
  }, [dispatch]);
  
  const loadConversation = useCallback((sessionId: string) => {
    return dispatch(fetchConversation(sessionId));
  }, [dispatch]);
  
  const selectConversation = useCallback((conversation: Conversation | null) => {
    dispatch(setCurrentConversation(conversation));
  }, [dispatch]);
  
  const createNewConversation = useCallback(() => {
    dispatch(startNewConversation());
  }, [dispatch]);
  
  const removeConversation = useCallback((sessionId: string) => {
    return dispatch(deleteConversation(sessionId));
  }, [dispatch]);
  
  const updateConversationTitleAction = useCallback((sessionId: string, title: string) => {
    return dispatch(updateConversationTitle({ id: sessionId, title }));
  }, [dispatch]);
  
  const searchConversationsAction = useCallback((searchRequest: import('../types/chat').SearchConversationsRequest) => {
    return dispatch(searchConversations(searchRequest));
  }, [dispatch]);
  
  // === AÇÕES DE MENSAGEM ===
  
  const sendChatMessage = useCallback((request: SendMessageRequest) => {
    return dispatch(sendMessage(request));
  }, [dispatch]);
  
  const sendQuickMessage = useCallback((content: string, files?: File[]) => {
    const request: SendMessageRequest = {
      content,
      provider: selectedProvider,
      model: selectedModel,
      files,
      sessionId: currentConversation?.sessionId,
    };
    return sendChatMessage(request);
  }, [sendChatMessage, selectedProvider, selectedModel, currentConversation]);
  
  const uploadChatFile = useCallback((file: File) => {
    return dispatch(uploadFile(file));
  }, [dispatch]);
  
  const addNewMessage = useCallback((message: Message) => {
    dispatch(addMessage(message));
  }, [dispatch]);
  
  const updateChatMessage = useCallback((messageId: string, content: string) => {
    dispatch(updateMessage({ messageId, content }));
  }, [dispatch]);
  
  const removeMessageAction = useCallback((messageId: string) => {
    dispatch(removeMessage(messageId));
  }, [dispatch]);

  const removeMessagesAfterAction = useCallback((messageId: string) => {
    dispatch(removeMessagesAfter(messageId));
  }, [dispatch]);
  
  const regenerateLastMessage = useCallback(() => {
    if (lastMessage && lastMessage.role === 'assistant') {
      // Encontrar a mensagem do usuário que gerou esta resposta
      const userMessageIndex = messages.findIndex(m => m.id === lastMessage.id) - 1;
      if (userMessageIndex >= 0) {
        const userMessage = messages[userMessageIndex];
        return sendQuickMessage(userMessage.content, userMessage.attachments?.map(a => new File([], a.name)));
      }
    }
  }, [lastMessage, messages, sendQuickMessage]);
  
  // === AÇÕES DE UI ===
  
  const setTyping = useCallback((typing: boolean) => {
    dispatch(setIsTyping(typing));
  }, [dispatch]);
  
  const setStreaming = useCallback((message: string | null) => {
    dispatch(setStreamingMessage(message));
  }, [dispatch]);
  
  const clearChatError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);
  
  // === AÇÕES DE SELEÇÃO ===
  
  const changeProvider = useCallback((provider: string) => {
    dispatch(setSelectedProvider(provider));
  }, [dispatch]);
  
  const changeModel = useCallback((model: string) => {
    dispatch(setSelectedModel(model));
  }, [dispatch]);
  
  // === AÇÕES DE BUSCA E FILTROS ===
  
  const updateSearchQuery = useCallback((query: string) => {
    dispatch(setSearchQuery(query));
  }, [dispatch]);
  
  const updateFilters = useCallback((newFilters: ConversationFilters) => {
    dispatch(setFilters(newFilters));
  }, [dispatch]);
  
  const resetFilters = useCallback(() => {
    dispatch(clearFilters());
  }, [dispatch]);
  
  const toggleConversationFavorite = useCallback((sessionId: string) => {
    dispatch(toggleFavorite(sessionId));
  }, [dispatch]);
  
  // === AÇÕES DE SESSÃO ===
  
  const setPendingSession = useCallback((sessionId: string | null) => {
    dispatch(setPendingSessionId(sessionId));
  }, [dispatch]);
  
  const applyPendingSession = useCallback(() => {
    dispatch(applyPendingSessionId());
  }, [dispatch]);
  
  // === HELPERS ===
  
  const getConversationById = useCallback((sessionId: string) => {
    const state = store.getState();
    return selectConversationById(state, sessionId);
  }, []);
  
  const isConversationFavorite = useCallback((sessionId: string) => {
    return favoriteConversations.includes(sessionId);
  }, [favoriteConversations]);
  
  const searchConversationsLocal = useCallback((query: string) => {
    updateSearchQuery(query);
  }, [updateSearchQuery]);
  
  const filterByProvider = useCallback((provider: string) => {
    updateFilters({ ...filters, provider });
  }, [filters, updateFilters]);
  
  const filterByDateRange = useCallback((start: Date, end: Date) => {
    updateFilters({ ...filters, dateRange: { start, end } });
  }, [filters, updateFilters]);
  
  const filterByFiles = useCallback((hasFiles: boolean) => {
    updateFilters({ ...filters, hasFiles });
  }, [filters, updateFilters]);
  
  const filterByFavorites = useCallback((isFavorite: boolean) => {
    updateFilters({ ...filters, isFavorite });
  }, [filters, updateFilters]);
  
  // Computed values
  const hasConversations = conversations.length > 0;
  const hasFavorites = favoriteConversations.length > 0;
  const filteredCount = filteredConversations.length;
  
  return {
    // === ESTADO ===
    
    // Conversas
    conversations,
    currentConversation,
    filteredConversations,
    favoriteConversationsList,
    recentConversations,
    conversationsByProvider,
    
    // Mensagens
    messages,
    lastMessage,
    messageCount,
    
    // UI
    isLoading,
    isTyping,
    streamingMessage,
    error,
    
    // Seleções
    selectedProvider,
    selectedModel,
    
    // Busca e filtros
    searchQuery,
    filters,
    favoriteConversations,
    hasMore,
    
    // Temporário
    pendingSessionId,
    
    // Computados
    hasMessages,
    canRegenerate,
    isStreaming,
    hasActiveFilters,
    hasConversations,
    hasFavorites,
    filteredCount,
    
    // === AÇÕES ===
    
    // Conversas
    loadConversations,
    loadConversation,
    selectConversation,
    createNewConversation,
    removeConversation,
    updateConversationTitleAction,
    searchConversationsAction,
    
    // Mensagens
    sendChatMessage,
    sendQuickMessage,
    uploadChatFile,
    addNewMessage,
    updateChatMessage,
    removeMessageAction,
    removeMessagesAfterAction,
    regenerateLastMessage,
    
    // UI
    setTyping,
    setStreaming,
    clearChatError,
    
    // Seleções
    changeProvider,
    changeModel,
    
    // Busca e filtros
    updateSearchQuery,
    updateFilters,
    resetFilters,
    toggleConversationFavorite,
    
    // Sessão
    setPendingSession,
    applyPendingSession,
    
    // Helpers
    getConversationById,
    isConversationFavorite,
    searchConversationsLocal,
    filterByProvider,
    filterByDateRange,
    filterByFiles,
    filterByFavorites,
  };
};

export default useChatConversations;
