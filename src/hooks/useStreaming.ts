import { useState, useCallback, useRef } from 'react';
import { useStreamProcessor } from './useStreamProcessor';
import { useMessageStreaming } from './useMessageStreaming';
import { chatApi } from '../api/chatApi';
import { logger } from '../shared/utils/logger';
import type { StreamingResponse } from '../types/chat';

interface StreamingOptions {
  modelId: string;
  modelName: string;
  provider: string;
  sessionId?: string;
  isRegeneration?: boolean;
  fileId?: string;
  onStart?: () => void;
  onComplete?: (message: string) => void;
  onError?: (error: Error) => void;
  onSessionCreated?: (sessionId: string) => void;
}

export const useStreaming = () => {
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { simulateStream, abortStream } = useStreamProcessor();
  const {
    startStreaming,
    handleStreamChunk,
    handleStreamComplete,
    clearStreaming,
    updateStreamingMessage
  } = useMessageStreaming();

  const currentContentRef = useRef('');
  const currentOptionsRef = useRef<StreamingOptions | null>(null);
  const isCompletedRef = useRef(false);
  const cancelFunctionRef = useRef<(() => void) | null>(null);






  const startStreamingMessage = useCallback(async (
    message: string,
    options: StreamingOptions
  ) => {
    if (isStreaming) return;

    try {
      setIsStreaming(true);
      setError(null);
      currentContentRef.current = '';
      currentOptionsRef.current = options;
      isCompletedRef.current = false; // Resetar flag para novo streaming
      
      logger.dev('Definindo opções no useStreaming', {
        modelId: options.modelId,
        modelName: options.modelName,
        provider: options.provider,
        hasOptions: !!currentOptionsRef.current
      });

      options.onStart?.();
      startStreaming();

      // Tentar usar WebSocket streaming primeiro
      try {
        // Usar WebSocket diretamente via chatApi
        const cancelFunction = chatApi.sendStreamMessage(
          {
            content: message,
            provider: options.provider,
            model: options.modelId,
            sessionId: options.sessionId,
            stream: true
          },
          (data: StreamingResponse) => {
            if (data.type === 'content' && data.content) {
              currentContentRef.current += data.content;
              updateStreamingMessage(currentContentRef.current);
            } else if (data.type === 'end') {
              // Apenas atualizar o conteúdo final, não chamar handleStreamComplete aqui
              const finalContent = data.content || currentContentRef.current;
              currentContentRef.current = finalContent;
              updateStreamingMessage(finalContent);
            }
          },
          (error: Error) => {
            setError(error.message);
            options.onError?.(error);
          },
          () => {
            const finalContent = currentContentRef.current;
            if (finalContent) {
              handleStreamComplete(finalContent, options.provider, options.modelName, {
                isRegeneration: options.isRegeneration,
              });
              options.onComplete?.(finalContent);
            }
            // Importante: Marcar streaming como finalizado
            setIsStreaming(false);
          },
          options.onSessionCreated,
          options.fileId
        );

        // Armazenar função de cancelamento
        cancelFunctionRef.current = cancelFunction;
        
        // Aguardar o streaming completar
        return;
      } catch (streamError) {
        logger.warn('WebSocket streaming falhou, usando fallback', streamError);
        
        // Fallback para simulação se WebSocket falhar
        const mockContent = `Resposta simulada para: ${message}`;
        
        await simulateStream(
          mockContent,
          (chunk) => {
            currentContentRef.current = handleStreamChunk(chunk, currentContentRef.current);
          },
          (fullMessage) => {
            handleStreamComplete(fullMessage, options.provider, options.modelName, {
              isRegeneration: options.isRegeneration,
            });
            options.onComplete?.(fullMessage);
          },
          () => {
            // Handle abort - preserve partial content or use placeholder
            const partialContent = currentContentRef.current;
            if (partialContent.trim()) {
              handleStreamComplete(partialContent, options.provider, options.modelName, { isRegeneration: false });
            } else {
              // Se não há conteúdo, usar placeholder
              handleStreamComplete('Sem resposta.', options.provider, options.modelName, { isRegeneration: false });
            }
            currentContentRef.current = '';
          }
        );
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Streaming failed');
      
      logger.error('Streaming error:', error);
      setError(error.message);
      options.onError?.(error);
      clearStreaming();
    } finally {
      setIsStreaming(false);
    }
  }, [
    isStreaming,
    updateStreamingMessage,
    startStreaming,
    simulateStream,
    handleStreamChunk,
    handleStreamComplete,
    clearStreaming
  ]);

  const stopStreaming = useCallback(() => {
    if (isStreaming) {
      // Preservar o conteúdo atual antes de parar
      const currentContent = currentContentRef.current;
      const options = currentOptionsRef.current;

      logger.dev('Parando streaming e preservando conteúdo', {
        contentLength: currentContent.length,
        hasOptions: !!options
      });

      // Cancelar streaming se houver função de cancelamento
      if (cancelFunctionRef.current) {
        cancelFunctionRef.current();
        cancelFunctionRef.current = null;
      } else {
        abortStream();
      }
      setIsStreaming(false);

      // Se há conteúdo parcial e opções, completar a mensagem com o conteúdo atual
      if (currentContent && options && !isCompletedRef.current) {
        isCompletedRef.current = true;

        handleStreamComplete(currentContent, options.provider, options.modelName, {
          isRegeneration: options.isRegeneration,
        });

        // Limpar após processar
        currentOptionsRef.current = null;
        currentContentRef.current = '';
      } else if (options && !isCompletedRef.current) {
        // Se não há conteúdo mas há opções (interrompido antes do streaming começar),
        // criar mensagem placeholder
        isCompletedRef.current = true;

        handleStreamComplete('Sem resposta.', options.provider, options.modelName, {
          isRegeneration: options.isRegeneration,
        });

        // Limpar após processar
        currentOptionsRef.current = null;
        currentContentRef.current = '';
      } else {
        // Se não há opções ou já foi completado, apenas limpar
        clearStreaming();
      }
    }
  }, [isStreaming, abortStream, clearStreaming, handleStreamComplete]);

  return {
    isStreaming,
    error,
    startStreaming: startStreamingMessage,
    stopStreaming,
  };
};

// Re-export useTypingEffect from its new location
export { useTypingEffect } from './useTypingEffect';