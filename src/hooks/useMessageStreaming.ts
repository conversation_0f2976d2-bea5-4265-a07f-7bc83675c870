import { useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '../redux/store';
import {
  setStreamingMessage,
  addMessage,
} from '../redux/chatConversations/chatConversationsReducer';
import { selectCurrentConversation } from '../redux/chatConversations/chatConversationsSelectors';
import type { Message } from '../types/chat';


export const useMessageStreaming = () => {
  const dispatch = useAppDispatch();
  const currentConversation = useAppSelector(selectCurrentConversation);

  const startStreaming = useCallback(() => {
    dispatch(setStreamingMessage(''));
  }, [dispatch]);

  const updateStreamingMessage = useCallback((content: string) => {
    dispatch(setStreamingMessage(content));
  }, [dispatch]);

  const completeMessage = useCallback((
    content: string,
    provider: string,
    model: string,
    _isRegeneration: boolean = false,
    tokens?: { prompt: number; completion: number; total: number },
    cost?: number
  ) => {
    const messageId = Math.random().toString(36).substr(2, 9);
    
    const message: Message = {
      id: messageId,
      content,
      role: 'assistant',
      timestamp: new Date().toISOString(),
      conversationId: currentConversation?.sessionId || '',
      provider,
      model,
      tokens,
      cost,
    };

    dispatch(addMessage(message));
    dispatch(setStreamingMessage(null));
  }, [dispatch, currentConversation?.sessionId]);

  const clearStreaming = useCallback(() => {
    dispatch(setStreamingMessage(null));
  }, [dispatch]);

  const handleStreamChunk = useCallback((chunk: string, currentContent: string) => {
    const newContent = currentContent + chunk;
    updateStreamingMessage(newContent);
    return newContent;
  }, [updateStreamingMessage]);

  const handleStreamComplete = useCallback((
    fullMessage: string,
    provider: string,
    model: string,
    options?: {
      isRegeneration?: boolean;
      tokens?: { prompt: number; completion: number; total: number };
      cost?: number;
    }
  ) => {
    completeMessage(
      fullMessage,
      provider,
      model,
      options?.isRegeneration || false,
      options?.tokens,
      options?.cost
    );
  }, [completeMessage]);

  return {
    startStreaming,
    updateStreamingMessage,
    completeMessage,
    clearStreaming,
    handleStreamChunk,
    handleStreamComplete,
  };
};