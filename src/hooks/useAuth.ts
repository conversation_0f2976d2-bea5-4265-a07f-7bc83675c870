import { useCallback } from 'react';
import { useNavigate, useLocation, type Location } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../redux/store';
import {
  loginUser,
  googleAuth,
  refreshToken,
  validateToken,
  logoutUser,
} from '../redux/auth/authActions';
import { clearError } from '../redux/auth/authReducer';
import {
  selectUser,
  selectIsAuthenticated,
  selectAuthLoading,
  selectAuthError,
  selectAuthToken,
  selectUserRole,
} from '../redux/auth/authSelectors';
import type { LoginCredentials } from '../api/authApi';
import { authApiReal, authRealUtils } from '../api/authApiReal';
import { shouldUseMockServer } from '../api/mockServer';

export const useAuth = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  // Selectors
  const user = useAppSelector(selectUser);
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const isLoading = useAppSelector(selectAuthLoading);
  const error = useAppSelector(selectAuthError);
  const token = useAppSelector(selectAuthToken);
  const userRole = useAppSelector(selectUserRole);

  // Actions
  const login = useCallback(
    async (credentials: LoginCredentials) => {
      const result = await dispatch(loginUser(credentials)).unwrap();
      // Redirecionar após login bem-sucedido
      const from =
        (location.state as { from?: Location })?.from?.pathname || '/studio';
      navigate(from, { replace: true });
      return result;
    },
    [dispatch, navigate, location.state]
  );

  const loginWithGoogle = useCallback(
    async (googleToken: string) => {
      if (shouldUseMockServer()) {
        const result = await dispatch(googleAuth(googleToken)).unwrap();
        // Redirecionar após login bem-sucedido
        const from =
          (location.state as { from?: Location })?.from?.pathname || '/studio';
        navigate(from, { replace: true });
        return result;
      } else {
        // Para API real, o token já foi processado no GoogleAuthHandler
        // Apenas validar o token atual
        const result = await dispatch(validateToken()).unwrap();
        return result;
      }
    },
    [dispatch, navigate, location.state]
  );

  const initiateGoogleLogin = useCallback(
    async () => {
      if (shouldUseMockServer()) {
        // Para mock, usar fluxo normal
        throw new Error('Use loginWithGoogle para mock server');
      } else {
        // Para API real, iniciar fluxo OAuth
        await authRealUtils.initiateGoogleLogin();
      }
    },
    []
  );

  const logout = useCallback(async () => {
    if (!shouldUseMockServer()) {
      // Para API real, chamar logout do backend
      try {
        await authApiReal.logout();
      } catch (error) {
        console.warn('Logout backend failed, continuing with local logout:', error);
      }
    }
    dispatch(logoutUser());
  }, [dispatch]);

  const refresh = useCallback(() => {
    return dispatch(refreshToken());
  }, [dispatch]);

  const validate = useCallback(() => {
    return dispatch(validateToken());
  }, [dispatch]);

  const clearAuthError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Permission system
  const hasPermission = useCallback(
    (action: string, workspacePermission?: 'owner' | 'editor' | 'viewer') => {
      switch (action) {
        case 'CREATE_WORKSPACE':
          return userRole === 'admin';
        case 'CREATE_PROJECT':
          return workspacePermission === 'owner';
        case 'ADD_DOCUMENT':
          return (
            workspacePermission === 'owner' || workspacePermission === 'editor'
          );
        case 'VIEW_PROJECTS':
          return (
            workspacePermission === 'owner' || workspacePermission === 'editor'
          );
        default:
          return false;
      }
    },
    [userRole]
  );

  // Computed values
  const isAdmin = userRole === 'admin';
  const isUser = userRole === 'user';

  return {
    // State
    user,
    isAuthenticated,
    isLoading,
    error,
    token,
    userRole,

    // Computed
    isAdmin,
    isUser,

    // Actions
    login,
    loginWithGoogle,
    initiateGoogleLogin,
    logout,
    refresh,
    validate,
    clearAuthError,
    hasPermission,
  };
};
