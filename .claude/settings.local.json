{"permissions": {"allow": ["Bash(npm run lint)", "Bash(npm run typecheck:*)", "Bash(npm run build:*)", "Bash(npm run dev:*)", "Bash(npm run type-check:*)", "Bash(rm:*)", "Bash(find:*)", "Bash(npm ls:*)", "Bash(npm audit:*)", "<PERSON><PERSON>(npx playwright:*)", "Bash(npm run test:*)", "Bash(./quick-check.sh:*)", "Bash(rg:*)", "Bash(grep:*)", "<PERSON><PERSON>(touch:*)", "Bash(npm install:*)", "Bash(node:*)", "Bash(ls:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(curl:*)", "Bash(npm test)", "Bash(git pull:*)"], "deny": []}, "$schema": "https://json.schemastore.org/claude-code-settings.json"}